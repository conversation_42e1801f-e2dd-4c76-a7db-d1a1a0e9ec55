<template>
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
    <!-- Table Header -->
    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">{{ title }}</h3>

          <!-- Limit Dropdown -->
          <div class="relative">
            <select v-model="selectedLimit" @change="handleLimitChange"
              class="block w-20 pl-3 pr-8 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
              <option v-for="limit in limitOptions" :key="limit" :value="limit">
                {{ limit }}
              </option>
            </select>
          </div>
        </div>

        <div class="flex items-center space-x-4">
          <!-- Search -->
          <div v-if="searchable" class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <input v-model="searchQuery" type="text" :placeholder="searchPlaceholder"
              class="block w-48 sm:w-64 pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 placeholder-gray-500 dark:placeholder-gray-400 dark:text-white focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm"
              @input="handleSearch" />
          </div>

          <!-- Header Actions Slot -->
          <slot name="header-actions"></slot>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex items-center justify-center py-12">
      <div class="flex items-center space-x-2 text-gray-500">
        <svg class="animate-spin h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
        </svg>
        <span>Loading...</span>
      </div>
    </div>

    <!-- Data Display -->
    <div v-else-if="data.length > 0">
      <!-- Desktop Table -->
      <div class="hidden md:block overflow-x-auto">
        <div class="inline-block min-w-full align-middle">
          <div class="overflow-visible shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
            <table class="min-w-full divide-y divide-gray-300">
              <!-- Table Header -->
              <thead class="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th v-for="(header, key) in tableHeaders" :key="key"
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200"
                    @click="handleSort(key)">
                    <div class="flex items-center space-x-1">
                      <span>{{ header }}</span>
                      <svg v-if="sortField === key" class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path v-if="sortDirection === 'asc'" stroke-linecap="round" stroke-linejoin="round"
                          stroke-width="2" d="M5 15l7-7 7 7" />
                        <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </th>
                  <th v-if="hasActions"
                    class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>

              <!-- Table Body -->
              <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                <tr v-for="(item, index) in displayData" :key="getRowKey(item, index)"
                  class="hover:bg-gray-50 dark:text-gray-300 dark:hover:bg-gray-700 transition-colors duration-200 cursor-pointer" :class="rowClass"
                  @click="handleRowClick(item, index)">
                  <td v-for="(_, key) in tableHeaders" :key="key" class="px-6 py-4 whitespace-nowrap text-sm"
                    :class="getCellClass(item[key])">
                    <!-- Custom Cell Content -->
                    <slot :name="`cell-${key}`" :item="item" :value="item[key]" :index="index">
                      <!-- Default Cell Content -->
                      <span v-if="typeof item[key] === 'boolean'">
                        <span v-if="item[key]"
                          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                          Yes
                        </span>
                        <span v-else
                          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200">
                          No
                        </span>
                      </span>
                      <span v-else-if="isDate(item[key])" class="text-gray-900 dark:text-gray-300">
                        {{ formatDate(item[key]) }}
                      </span>
                      <span v-else-if="isNumber(item[key])" class="text-gray-900 dark:text-gray-300 font-medium">
                        {{ formatNumber(item[key]) }}
                      </span>
                      <span v-else class="text-gray-900 dark:text-gray-300">
                        {{ props.autoFormat && !$slots[`cell-${key}`] ? formatCellValue(item[key], key) : (item[key] ||
                        '-') }}
                      </span>
                    </slot>
                  </td>

                  <!-- Actions Column -->
                  <td v-if="hasActions" class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium relative">
                    <div class="relative inline-block text-left">
                      <button @click.stop="toggleDropdown(index)"
                        class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                        :class="{ 'bg-gray-200': openDropdowns[index] }">
                        <svg class="w-4 h-4 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                          <path
                            d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
                        </svg>
                      </button>

                      <!-- Dropdown Menu -->
                      <div v-if="openDropdowns[index]"
                        class="absolute right-0 z-[60] mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 focus:outline-none max-h-60 overflow-y-auto"
                        :class="getDropdownPositionClass(index)" @click.stop>
                        <div class="py-1">
                          <slot name="actions" :item="item" :index="index" :closeDropdown="() => closeDropdown(index)">
                            <button
                              class="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                              View
                            </button>
                          </slot>
                        </div>
                      </div>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Mobile Cards -->
      <div class="md:hidden divide-y divide-gray-200 dark:divide-gray-700">
        <div v-for="(item, index) in displayData" :key="getRowKey(item, index)"
          class="p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200 cursor-pointer" :class="rowClass"
          @click="handleRowClick(item, index)">
        <!-- Mobile Card Content -->
        <div class="space-y-3">
          <div v-for="(header, key) in tableHeaders" :key="key" class="flex justify-between items-start">
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 w-1/3">{{ header }}</dt>
            <dd class="text-sm text-gray-900 dark:text-gray-100 w-2/3 text-right">
              <!-- Custom Cell Content -->
              <slot :name="`cell-${key}`" :item="item" :value="item[key]" :index="index" class="dark:text-gray-300">
                <!-- Default Cell Content -->
                <span v-if="typeof item[key] === 'boolean'">
                  <span v-if="item[key]"
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Yes
                  </span>
                  <span v-else
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                    No
                  </span>
                </span>
                <span v-else-if="isDate(item[key])" class="text-gray-900">
                  {{ formatDate(item[key]) }}
                </span>
                <span v-else-if="isNumber(item[key])" class="text-gray-900 font-medium">
                  {{ formatNumber(item[key]) }}
                </span>
                <span v-else class="text-gray-900">
                  {{ item[key] || '-' }}
                </span>
              </slot>
            </dd>
          </div>

          <!-- Mobile Actions -->
          <div v-if="hasActions" class="pt-3 border-t border-gray-200 dark:border-gray-700">
            <div class="relative inline-block text-left">
              <button @click.stop="toggleDropdown(`mobile-${index}`)"
                class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                :class="{ 'bg-gray-200 dark:bg-gray-600': openDropdowns[`mobile-${index}`] }">
                <svg class="w-4 h-4 text-gray-600 dark:text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
                </svg>
              </button>

              <!-- Mobile Dropdown Menu -->
              <div v-if="openDropdowns[`mobile-${index}`]"
                class="absolute left-0 z-[60] mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black dark:ring-white ring-opacity-5 dark:ring-opacity-10 focus:outline-none max-h-60 overflow-y-auto"
                @click.stop>
                <div class="py-1">
                  <slot name="actions" :item="item" :index="index"
                    :closeDropdown="() => closeDropdown(`mobile-${index}`)">
                    <button
                      class="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                      View
                    </button>
                  </slot>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>


    <!-- Pagination -->
    <div v-if="pagination && totalRecords > 0" class="bg-white dark:bg-gray-800 px-4 py-3 border-t border-gray-200 dark:border-gray-700 sm:px-6">
      <div class="flex items-center justify-between">
        <!-- Results Info -->
        <div class="flex-1 flex justify-between sm:hidden">
          <button @click="previousPage" :disabled="currentPage === 1"
            class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed">
            Previous
          </button>
          <button @click="nextPage" :disabled="currentPage === totalPages"
            class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed">
            Next
          </button>
        </div>

        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <p class="text-sm text-gray-700 dark:text-gray-300">
              Showing
              <span class="font-medium">{{ startRecord }}</span>
              to
              <span class="font-medium">{{ endRecord }}</span>
              of
              <span class="font-medium">{{ totalRecords }}</span>
              results
            </p>
          </div>

          <!-- Pagination Controls -->
          <div>
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
              <!-- Previous Button -->
              <button @click="previousPage" :disabled="currentPage === 1"
                class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed">
                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
              </button>

              <!-- Page Numbers -->
              <button v-for="page in visiblePages" :key="page" @click="goToPage(page)" :class="[
                'relative inline-flex items-center px-4 py-2 border text-sm font-medium transition-colors duration-200',
                page === currentPage
                  ? 'z-10 bg-blue-50 dark:bg-blue-900 border-blue-500 dark:border-blue-400 text-blue-600 dark:text-blue-300'
                  : 'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700'
              ]">
                {{ page }}
              </button>

              <!-- Next Button -->
              <button @click="nextPage" :disabled="currentPage === totalPages"
                class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed">
                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </nav>
          </div>
        </div>
      </div>
    </div>
  </div>  
    <!-- Empty State -->
    <div v-else class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900">No data found</h3>
      <p class="mt-1 text-sm text-gray-500">{{ emptyMessage }}</p>
    </div>
  </div>

</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { autoFormat } from '@/utils/formatters'

// Props
interface Props {
  data: any[]
  headers?: Record<string, string>
  title?: string
  loading?: boolean
  searchable?: boolean
  pagination?: boolean
  currentPage?: number
  totalRecords?: number
  pageSize?: number
  rowKey?: string
  rowClass?: string
  emptyMessage?: string
  hasActions?: boolean
  excludeColumns?: string[]
  autoFormat?: boolean
  // Enhanced search options
  searchMode?: 'api' | 'local'
  searchFields?: string[]
  searchMinLength?: number
  searchPlaceholder?: string
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [],
  headers: () => ({}),
  title: 'Data Table',
  loading: false,
  searchable: true,
  pagination: true,
  currentPage: 1,
  totalRecords: 0,
  pageSize: 10,
  rowKey: 'id',
  rowClass: '',
  emptyMessage: 'No records found',
  hasActions: false,
  excludeColumns: () => [],
  autoFormat: true,
  // Enhanced search defaults
  searchMode: 'api',
  searchFields: () => [],
  searchMinLength: 3,
  searchPlaceholder: 'Search...'
})

// Emits
const emit = defineEmits<{
  'page-change': [page: number]
  'search': [query: string]
  'sort': [field: string, direction: 'asc' | 'desc']
  'row-click': [item: any, index: number]
  'limit-change': [limit: number]
}>()

// Reactive data
const searchQuery = ref('')
const sortField = ref('')
const sortDirection = ref<'asc' | 'desc'>('asc')
const openDropdowns = ref<Record<string | number, boolean>>({})
const selectedLimit = ref(props.pageSize)

// Auto-generate headers from data if not provided
const computedHeaders = computed(() => {
  if (Object.keys(props.headers || {}).length > 0) {
    return props.headers
  }

  if (displayData.value.length === 0) {
    return {}
  }

  // Get all unique keys from data
  const allKeys = new Set<string>()
  props.data.forEach(item => {
    Object.keys(item).forEach(key => allKeys.add(key))
  })

  // Filter out excluded columns
  const filteredKeys = Array.from(allKeys).filter(key =>
    !props.excludeColumns?.includes(key)
  )

  // Generate headers with proper formatting
  const headers: Record<string, string> = {}
  filteredKeys.forEach(key => {
    headers[key] = key
      .replace(/[_-]/g, ' ')
      .replace(/\b\w/g, l => l.toUpperCase())
  })

  return headers
})

// Limit options
const limitOptions = [10, 25, 50, 100]

// Computed properties
const tableHeaders = computed(() => {
  return computedHeaders.value
})

const totalPages = computed(() => Math.ceil(props.totalRecords / props.pageSize))

const startRecord = computed(() => (props.currentPage - 1) * props.pageSize + 1)

const endRecord = computed(() => Math.min(props.currentPage * props.pageSize, props.totalRecords))

const visiblePages = computed(() => {
  const pages = []
  const maxVisible = 5
  let start = Math.max(1, props.currentPage - Math.floor(maxVisible / 2))
  let end = Math.min(totalPages.value, start + maxVisible - 1)

  if (end - start + 1 < maxVisible) {
    start = Math.max(1, end - maxVisible + 1)
  }

  for (let i = start; i <= end; i++) {
    pages.push(i)
  }

  return pages
})

// Computed for filtered data (local search)
const filteredData = computed(() => {
  if (props.searchMode === 'local' && searchQuery.value.length >= props.searchMinLength) {
    const query = searchQuery.value.toLowerCase()
    return props.data.filter(item => {
      // If specific search fields are defined, search only those fields
      if (props.searchFields.length > 0) {
        return props.searchFields.some(field => {
          const value = item[field]
          return value && value.toString().toLowerCase().includes(query)
        })
      }
      // Otherwise search all string fields
      return Object.values(item).some(value =>
        value && value.toString().toLowerCase().includes(query)
      )
    })
  }
  return props.data
})

// Use filtered data for display
const displayData = computed(() => {
  return props.searchMode === 'local' ? filteredData.value : props.data
})

// Methods
const handleSearch = () => {
  // For API search, only emit if minimum length is met
  if (props.searchMode === 'api') {
    if (searchQuery.value.length >= props.searchMinLength || searchQuery.value.length === 0) {
      emit('search', searchQuery.value)
    }
  }
  // For local search, filtering is handled by computed property
}

const handleSort = (field: string) => {
  if (sortField.value === field) {
    sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc'
  } else {
    sortField.value = field
    sortDirection.value = 'asc'
  }

  emit('sort', field, sortDirection.value)
}

const handleRowClick = (item: any, index: number) => {
  emit('row-click', item, index)
}

const getRowKey = (item: any, index: number) => {
  return item[props.rowKey] || index
}

const formatCellValue = (value: any, key: string): string => {
  return autoFormat(value, key)
}

const getCellClass = (value: any) => {
  const baseClass = 'text-gray-900'

  if (typeof value === 'boolean') {
    return baseClass
  } else if (typeof value === 'number') {
    return `${baseClass} font-medium`
  }

  return baseClass
}

const isDate = (value: any) => {
  // Only consider actual Date objects or strings that look like dates (not just any parseable string)
  if (value instanceof Date) return true

  if (typeof value === 'string') {
    // Exclude simple numbers or very short strings
    if (/^\d+$/.test(value.trim()) || value.trim().length < 8) return false

    // Check for common date patterns
    const datePatterns = [
      /^\d{4}-\d{2}-\d{2}/, // YYYY-MM-DD
      /^\d{2}\/\d{2}\/\d{4}/, // MM/DD/YYYY or DD/MM/YYYY
      /^\d{2}-\d{2}-\d{4}/, // MM-DD-YYYY or DD-MM-YYYY
      /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/, // ISO datetime
      /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/ // SQL datetime
    ]

    return datePatterns.some(pattern => pattern.test(value.trim())) && !isNaN(Date.parse(value))
  }

  return false
}

const isNumber = (value: any) => {
  return typeof value === 'number' && !isNaN(value)
}

const formatDate = (value: any) => {
  const date = new Date(value)
  return date.toLocaleDateString()
}

const formatNumber = (value: number) => {
  return value.toLocaleString()
}

const goToPage = (page: number) => {
  emit('page-change', page)
}

const previousPage = () => {
  if (props.currentPage > 1) {
    emit('page-change', props.currentPage - 1)
  }
}

const nextPage = () => {
  if (props.currentPage < totalPages.value) {
    emit('page-change', props.currentPage + 1)
  }
}

// Dropdown methods
const toggleDropdown = (index: string | number) => {
  // Close all other dropdowns
  Object.keys(openDropdowns.value).forEach(key => {
    if (key !== index.toString()) {
      openDropdowns.value[key] = false
    }
  })
  // Toggle current dropdown
  openDropdowns.value[index] = !openDropdowns.value[index]
}

const closeDropdown = (index: string | number) => {
  openDropdowns.value[index] = false
}

const closeAllDropdowns = () => {
  Object.keys(openDropdowns.value).forEach(key => {
    openDropdowns.value[key] = false
  })
}

// Limit change handler
const handleLimitChange = () => {
  emit('limit-change', selectedLimit.value)
}

// Get dropdown position class to prevent off-screen positioning
const getDropdownPositionClass = (_index: number) => {
  // For now, return empty string. In a more advanced implementation,
  // we could calculate if the dropdown would go off-screen and adjust positioning
  return ''
}

// Watch for search query changes with debounce
let searchTimeout: number
watch(searchQuery, () => {
  clearTimeout(searchTimeout)
  searchTimeout = setTimeout(() => {
    handleSearch()
  }, 300) as unknown as number
})

// Watch for pageSize prop changes
watch(() => props.pageSize, (newSize) => {
  selectedLimit.value = newSize
})

// Close dropdowns when clicking outside
onMounted(() => {
  document.addEventListener('click', closeAllDropdowns)
})

// Cleanup event listener
onUnmounted(() => {
  document.removeEventListener('click', closeAllDropdowns)
})
</script>
