<template>
  <div class="space-y-4">
    <label class="block text-sm font-medium text-gray-700 mb-2">
      {{ isAdminUser ? 'Assign Partners' : 'Assigned Partner' }}
      <span v-if="isRequired" class="text-red-500">*</span>
    </label>

    <!-- Admin User: Partner Search and Selection -->
    <div v-if="isAdminUser" class="space-y-4">
      <!-- Search Fields -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <input
            v-model="searchName"
            @input="debouncedSearch"
            type="text"
            placeholder="Search by partner name..."
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
          />
        </div>
        <div>
          <input
            v-model="searchCountry"
            @input="debouncedSearch"
            type="text"
            placeholder="Search by country..."
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
          />
        </div>
      </div>

      <!-- Search Results -->
      <div class="border border-gray-200 rounded-md max-h-64 overflow-y-auto">
        <!-- Loading State -->
        <div v-if="searching" class="p-4 text-center text-gray-500">
          <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
          <span class="mt-2 block">Searching partners...</span>
        </div>

        <!-- No Search Query -->
        <div v-else-if="!hasSearchQuery" class="p-4 text-center text-gray-500">
          Enter partner name or country to search
        </div>

        <!-- No Results -->
        <div v-else-if="searchResults.length === 0 && !searching" class="p-4 text-center text-gray-500">
          No partners found matching your search
        </div>

        <!-- Search Results -->
        <div v-else class="divide-y divide-gray-200">
          <label
            v-for="partner in searchResults"
            :key="partner.id"
            class="flex items-center space-x-3 p-3 hover:bg-gray-50 cursor-pointer"
          >
            <input
              type="checkbox"
              :value="partner.id"
              v-model="selectedPartnerIds"
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <div class="flex-1 min-w-0">
              <div class="text-sm font-medium text-gray-900">{{ partner.name }}</div>
              <div class="text-xs text-gray-500 space-x-2">
                <span v-if="partner.country">{{ partner.country }}</span>
                <span v-if="partner.address">{{ partner.address }}</span>
                <span v-if="partner.msisdn">{{ partner.msisdn }}</span>
              </div>
            </div>
            <div class="text-xs text-gray-400">
              ID: {{ partner.id }}
            </div>
          </label>
        </div>
      </div>

      <!-- Selected Partners Summary -->
      <div v-if="selectedPartnerIds.length > 0" class="bg-blue-50 p-3 rounded-md">
        <h4 class="text-sm font-medium text-blue-900 mb-2">
          Selected Partners ({{ selectedPartnerIds.length }})
        </h4>
        <div class="flex flex-wrap gap-2">
          <span
            v-for="partnerId in selectedPartnerIds"
            :key="partnerId"
            class="inline-flex items-center px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
          >
            {{ getPartnerName(partnerId) }}
            <button
              type="button"
              @click="removePartner(partnerId)"
              class="ml-1 text-blue-600 hover:text-blue-800 focus:outline-none"
            >
              ×
            </button>
          </span>
        </div>
      </div>
    </div>

    <!-- Partner User: Read-only Display -->
    <div v-else class="space-y-2">
      <div v-if="loading" class="text-gray-500">
        Loading assigned partners...
      </div>
      
      <div v-else-if="userPartners.length === 0" class="text-gray-500">
        No partners assigned
      </div>
      
      <div v-else class="space-y-2">
        <div
          v-for="partner in userPartners"
          :key="partner.id"
          class="flex items-center justify-between p-3 bg-gray-50 rounded-md"
        >
          <div>
            <div class="text-sm font-medium text-gray-900">{{ partner.name }}</div>
            <div class="text-xs text-gray-500 space-x-2">
              <span v-if="partner.country">{{ partner.country }}</span>
              <span v-if="partner.address">{{ partner.address }}</span>
            </div>
          </div>
          <div class="text-xs text-gray-400">
            ID: {{ partner.id }}
          </div>
        </div>
      </div>
    </div>

    <!-- Error Display -->
    <div v-if="error" class="mt-2 text-sm text-red-600">
      {{ error }}
    </div>

    <!-- Validation Error -->
    <div v-if="validationError" class="mt-2 text-sm text-red-600">
      {{ validationError }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { partnerApi } from '@/services/partnerApi'
import { useAuthStore } from '@/stores/auth'

// Props
interface Props {
  modelValue?: number[]
  userType?: 'Admin' | 'Partner'
  required?: boolean
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  userType: 'Partner',
  required: false,
  disabled: false
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: number[]]
  'partners-changed': [partners: any[]]
  'validation-error': [error: string | null]
}>()

// Store
const authStore = useAuthStore()

// Local state
const selectedPartnerIds = ref<number[]>([...props.modelValue])
const searchResults = ref<any[]>([])
const userPartners = ref<any[]>([])
const searchName = ref('')
const searchCountry = ref('')
const searching = ref(false)
const loading = ref(false)
const error = ref<string | null>(null)
const validationError = ref<string | null>(null)

// Debounce timer
let searchTimeout: ReturnType<typeof setTimeout> | null = null

// Computed
const isAdminUser = computed(() => {
  return props.userType === 'Admin' || authStore.isSuperUser
})

const isRequired = computed(() => {
  return props.required && !props.disabled
})

const hasSearchQuery = computed(() => {
  return searchName.value.trim().length > 0 || searchCountry.value.trim().length > 0
})

// Methods
const searchPartners = async () => {
  if (!hasSearchQuery.value) {
    searchResults.value = []
    return
  }

  searching.value = true
  error.value = null

  try {
    const params: any = {
      limit: 50 // Limit search results
    }

    if (searchName.value.trim()) {
      params.name = searchName.value.trim()
    }

    if (searchCountry.value.trim()) {
      params.country = searchCountry.value.trim()
    }

    const response = await partnerApi.getPartners(params)

    if (response.status === 200) {
      searchResults.value = response.message?.data || []
    } else {
      error.value = 'Failed to search partners'
      searchResults.value = []
    }
  } catch (err: any) {
    error.value = err.message || 'Failed to search partners'
    searchResults.value = []
  } finally {
    searching.value = false
  }
}

const debouncedSearch = () => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }

  searchTimeout = setTimeout(() => {
    searchPartners()
  }, 300) // 300ms debounce
}

const getPartnerName = (partnerId: number): string => {
  const partner = searchResults.value.find(p => p.id === partnerId) ||
                  userPartners.value.find(p => p.id === partnerId)
  return partner?.name || `Partner ${partnerId}`
}

const removePartner = (partnerId: number) => {
  selectedPartnerIds.value = selectedPartnerIds.value.filter(id => id !== partnerId)
  emit('update:modelValue', selectedPartnerIds.value)
}

const validateSelection = () => {
  if (isRequired.value && selectedPartnerIds.value.length === 0) {
    validationError.value = 'At least one partner must be selected'
    emit('validation-error', validationError.value)
    return false
  }
  
  validationError.value = null
  emit('validation-error', null)
  return true
}

const loadUserPartners = async () => {
  if (isAdminUser.value) return

  loading.value = true
  try {
    // For partner users, load their assigned partners
    const userPartnerIds = authStore.user?.partners?.map(p => p.id) || []
    
    if (userPartnerIds.length > 0) {
      // Fetch partner details
      const response = await partnerApi.getPartners({ 
        partner_ids: userPartnerIds.join(','),
        limit: 100 
      })
      
      if (response.status === 200) {
        userPartners.value = response.message?.data || []
        // Auto-select user's partners
        selectedPartnerIds.value = userPartnerIds
        emit('update:modelValue', selectedPartnerIds.value)
      }
    }
  } catch (err: any) {
    error.value = err.message || 'Failed to load assigned partners'
  } finally {
    loading.value = false
  }
}

// Watch for changes
watch(selectedPartnerIds, (newIds) => {
  emit('update:modelValue', newIds)
  
  const selectedPartners = searchResults.value.filter(p => 
    newIds.includes(p.id)
  )
  emit('partners-changed', selectedPartners)
  
  validateSelection()
}, { deep: true })

watch(() => props.modelValue, (newValue) => {
  selectedPartnerIds.value = [...newValue]
})

// Initialize
onMounted(async () => {
  await loadUserPartners()
})

// Cleanup
onUnmounted(() => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
})

// Expose validation method
defineExpose({
  validate: validateSelection
})
</script>
