<template>
  <header class="bg-gradient-to-br from-[#20ad79]/20 to-[#182232]/20 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 shadow-sm relative z-30">
    <div class="flex items-center justify-between h-16 px-4 sm:px-6">
      <!-- Left side: Hamburger menu and page title -->
      <div class="flex items-center space-x-2 sm:space-x-4 flex-1 min-w-0">
        <!-- Hamburger Menu Button -->
        <button
          @click="sidebarStore.toggle()"
          class="p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 flex-shrink-0"
          aria-label="Toggle sidebar"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
          </svg>
        </button>

        <!-- Page Title -->
        <div class="flex items-center space-x-2 min-w-0 flex-1">
          <h1 class="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white truncate">{{ pageTitle }}</h1>
          <div v-if="breadcrumbs.length > 1" class="hidden sm:flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-300">
            <svg class="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
            </svg>
            <span class="truncate">{{ breadcrumbs.join(' / ') }}</span>
          </div>
        </div>
      </div>

      <!-- Right side: Search, notifications, and user menu -->
      <div class="flex items-center space-x-2 sm:space-x-4 flex-shrink-0">
        <!-- Search Bar -->
        <div class="relative hidden lg:block">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
            </svg>
          </div>
          <input
            type="text"
            placeholder="Search..."
            class="block w-48 xl:w-64 pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <!-- Mobile Search Button -->
        <button class="p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors duration-200 lg:hidden">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
          </svg>
        </button>

        <!-- Theme Toggle -->
        <button
          @click="themeStore.toggleTheme()"
          class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
          :title="themeTooltip"
        >
          <!-- Light mode icon -->
          <svg v-if="themeStore.currentTheme === 'light'" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"/>
          </svg>
          <!-- Dark mode icon -->
          <svg v-else-if="themeStore.currentTheme === 'dark'" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"/>
          </svg>
          <!-- System mode icon -->
          <svg v-else class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
          </svg>
        </button>

        <!-- Notifications -->
        <button class="p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors duration-200 relative">
          <svg class="w-5 h-5 sm:w-6 sm:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.5 3.75a6 6 0 0 1 6 6v2.25l2.25 2.25v2.25H2.25v-2.25L4.5 12V9.75a6 6 0 0 1 6-6z"/>
          </svg>
          <!-- Notification badge -->
          <span class="absolute top-1 right-1 block h-2 w-2 rounded-full bg-red-400"></span>
        </button>

        <!-- Partner Selection (if multiple partners) -->
        <div v-if="authStore.hasMultiplePartners" class="relative mr-2">
          <button @click="showPartnerDropdown = !showPartnerDropdown"
            class="flex items-center space-x-2 px-3 py-2 text-sm rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
            </svg>
            <span class="max-w-32 truncate">{{ authStore.selectedPartner?.name || 'Select Partner' }}</span>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
            </svg>
          </button>

          <!-- Partner Dropdown -->
          <div v-if="showPartnerDropdown" class="absolute right-0 z-50 mt-2 w-64 bg-white dark:bg-gray-800 rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
            <div class="py-1">
              <button
                @click="selectPartner('all')"
                :class="[
                  'w-full text-left px-4 py-2 text-sm flex items-center transition-colors duration-200',
                  authStore.selectedPartnerId === 'all'
                    ? 'bg-blue-50 dark:bg-blue-900 text-blue-600 dark:text-blue-300'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                ]"
              >
                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                </svg>
                <div>
                  <div class="font-medium">All Partners</div>
                  <div class="text-xs text-gray-500 dark:text-gray-400">View data from all partners</div>
                </div>
              </button>
              <button
                v-for="partner in authStore.partnerList"
                :key="partner.id"
                @click="selectPartner(partner.id.toString())"
                :class="[
                  'w-full text-left px-4 py-2 text-sm flex items-center transition-colors duration-200',
                  authStore.selectedPartnerId === partner.id.toString()
                    ? 'bg-blue-50 dark:bg-blue-900 text-blue-600 dark:text-blue-300'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                ]"
              >
                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                </svg>
                <div>
                  <div class="font-medium">{{ partner.name }}</div>
                  <div class="text-xs text-gray-500 dark:text-gray-400">ID: {{ partner.id }}</div>
                </div>
              </button>
            </div>
          </div>
        </div>

        <!-- User Menu -->
        <div class="relative">
          <button
            @click="showUserMenu = !showUserMenu"
            class="flex items-center space-x-2 sm:space-x-3 p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200"
          >
            <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
              <span class="text-white text-sm font-medium">{{ userInitials }}</span>
            </div>
            <div class="hidden sm:block text-left min-w-0">
              <div class="text-sm font-medium text-gray-900 dark:text-white truncate">{{ userName }}</div>
              <div class="text-xs text-gray-500 dark:text-gray-300 truncate">{{ userRoleName || companyName || userRole }}</div>
            </div>
            <svg class="w-4 h-4 text-gray-400 hidden sm:block" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
            </svg>
          </button>

          <!-- User Dropdown Menu -->
          <Transition
            enter-active-class="transition ease-out duration-100"
            enter-from-class="transform opacity-0 scale-95"
            enter-to-class="transform opacity-100 scale-100"
            leave-active-class="transition ease-in duration-75"
            leave-from-class="transform opacity-100 scale-100"
            leave-to-class="transform opacity-0 scale-95"
          >
            <div
              v-if="showUserMenu"
              class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 py-1 z-50"
            >
              <a href="#" class="block px-4 py-2 text-sm text-gray-700 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700">Profile</a>
              <a href="#" class="block px-4 py-2 text-sm text-gray-700 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700">Settings</a>

              <!-- Privacy Mode Toggle -->
              <button
                @click="authStore.togglePrivateMode()"
                class="w-full text-left flex items-center justify-between px-4 py-2 text-sm text-gray-700 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                <span class="flex items-center">
                  <span class="mr-2">{{ authStore.isPrivateMode ? '👁️‍🗨️' : '👁️' }}</span>
                  Privacy Mode
                </span>
                <span class="text-xs px-2 py-1 rounded-full" :class="authStore.isPrivateMode ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'">
                  {{ authStore.isPrivateMode ? 'ON' : 'OFF' }}
                </span>
              </button>

              <hr class="my-1">
              <button
                @click="handleLogout"
                :disabled="isLoggingOut"
                class="w-full text-left block px-4 py-2 text-sm text-gray-700 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {{ isLoggingOut ? 'Signing out...' : 'Sign out' }}
              </button>
            </div>
          </Transition>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useSidebarStore } from '@/stores/sidebar'
import { useAuthStore } from '@/stores/auth'
import { useThemeStore } from '@/stores/theme'

const route = useRoute()
const router = useRouter()
const sidebarStore = useSidebarStore()
const authStore = useAuthStore()
const themeStore = useThemeStore()
const showUserMenu = ref(false)
const showPartnerDropdown = ref(false)
const isLoggingOut = ref(false)

// Page titles mapping
const pageTitles: Record<string, string> = {
  'dashboard': 'Dashboard',
  'organisations': 'Organizations',
  'organisations-config': 'Organization Configuration',
  'organisations-bulk': 'Organization Bulk SMS',
  'clients': 'Clients',
  'clients-config': 'Client Configuration',
  'clients-bulk': 'Client Bulk SMS',
  'merchants': 'Merchants',
  'merchants-config': 'Merchant Configuration',
  'merchants-bulk': 'Merchant Bulk SMS',
  'requests': 'Loan Requests',
  'limits': 'Loan Limits',
  'check-off': 'Check-off',
  'loan-accounts': 'Loan Accounts',
  'loan-products': 'Loan Products',
  'loan-repayments': 'Loan Repayments',
  'transactions': 'Transactions',
  'withdrawals': 'Withdrawals',
  'bill-payments': 'Bill Payments',
  'bill-payments-add': 'Add Bill Payment',
  'customers': 'Customer Search',
  'partners': 'Partners',
  'partner-services': 'Partner Services',
  'partners-bets': 'Partners Bets',
  'partners-bet-slips': 'Partners Bet Slips',
  'services': 'Services',
  'system-roles': 'System Roles',
  'system-permissions': 'System Permissions'
}

const pageTitle = computed(() => {
  return pageTitles[route.name as string] || 'Mossbets B2B Dashboard'
})

// User information from auth store
const userName = computed(() => {
  // Priority: un (display name) > username > name > fallback
  const rawName = authStore.user?.display_name || authStore.user?.username || 'User'
  return rawName
  // Convert underscores to spaces and capitalize words for better display
  // return rawName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
})

const userInitials = computed(() => {
  const name = userName.value
  return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)
})

// const userRole = computed(() => {
//   return authStore.role_id || 'Role'
// })

const userRoleName = computed(() => {
  // Priority: rname (role name from API) > role_name > fallback
  return authStore.user?.rname || authStore.user?.role_name || ''
})

const userRole = computed(() => {
  return authStore.user?.user_type || 'User'
})

const companyName = computed(() => {
  return authStore.user?.cn || ''
})

const breadcrumbs = computed(() => {
  const routeName = route.name as string
  const breadcrumbMap: Record<string, string[]> = {
    'partners': ['Partners'],
    'partner-services': ['Partners', 'Services'],
    'partners-bets': ['Partners', 'Bets'],
    'partners-bet-slips': ['Partners', 'Bet Slips'],
    'services': ['Services'],
    'system-roles': ['System', 'Roles'],
    'system-permissions': ['System', 'Permissions']
  }

  return breadcrumbMap[routeName] || []
})

const themeTooltip = computed(() => {
  const themeLabels = {
    light: 'Switch to dark mode',
    dark: 'Switch to system mode',
    system: 'Switch to light mode'
  }
  return themeLabels[themeStore.theme]
})

// Handle partner selection
const selectPartner = async (partnerId: string) => {
  await authStore.selectPartner(partnerId)
  showPartnerDropdown.value = false
  // Optionally refresh current page data
  // router.go(0) // Uncomment if you want to refresh the page
}

// Handle logout
const handleLogout = async () => {
  if (isLoggingOut.value) return

  try {
    isLoggingOut.value = true
    showUserMenu.value = false // Close the dropdown
    await authStore.logout()
    // Navigation will be handled by the auth store
  } catch (error) {
    console.error('Logout error:', error)
    // Force navigation to login even if logout fails
    router.push({ name: 'login' })
  } finally {
    isLoggingOut.value = false
  }
}

// Close dropdowns when clicking outside
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.relative')) {
    showUserMenu.value = false
    showPartnerDropdown.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>
