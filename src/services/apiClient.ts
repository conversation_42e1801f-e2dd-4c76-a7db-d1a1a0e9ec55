import axios, { type AxiosInstance, type AxiosRequestConfig, type InternalAxiosRequestConfig } from 'axios'
import { getAppKey, getAuthKey } from '@/utils/hash'
import {
  getApiBaseUrl as getConfiguredApiBaseUrl,
  getApiTimeout,
  isApiDebugEnabled
} from '@/config/api'

// API Configuration
const API_BASE_URL = getConfiguredApiBaseUrl()

/**
 * Create axios instance with default configuration
 */
export const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: getApiTimeout(),
  headers: {
    'Content-Type': 'application/json',
  }
})

/**
 * Request interceptor to add authentication headers
 */
apiClient.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // Get authentication data from localStorage
    const token = localStorage.getItem('token')
    const selectedClientId = localStorage.getItem('selectedClientId')
    const selectedPartnerId = localStorage.getItem('selectedPartnerId')
    const clientMode = localStorage.getItem('clientMode')

    // Add standard headers
    config.headers = config.headers || {}
    config.headers['X-App-Key'] = getAppKey()
    config.headers['X-Authorization'] = getAuthKey()
    // Removed X-Web-Auth header to resolve CORS issues

    // Add token if available
    if (token) {
      config.headers['X-Access'] = token
    } else {
      // Log warning if token is missing for authenticated endpoints
      if (isApiDebugEnabled()) {
        console.warn('⚠️ No authentication token found for API request:', config.url)
      }
    }

    // Add client context if available
    if (selectedClientId) {
      config.headers['X-Client-ID'] = selectedClientId
    }

    // Note: X-Partner-ID header removed as backend doesn't expect it
    // Partner filtering will be handled via partner_ids parameter in request payload

    if (clientMode) {
      config.headers['X-Client-Mode'] = clientMode
    }

    // For POST requests, also include access_token in the request body if token exists
    // This is required for some endpoints that expect the token in the payload
    if (config.method === 'post' && token && config.data) {
      // Only add if not already present in the data
      if (typeof config.data === 'object' && !config.data.access_token) {
        config.data = {
          ...config.data,
          access_token: token
        }
      }
    }

    // Enhanced logging for debugging network issues
    if (isApiDebugEnabled()) {
      console.group(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`)
      // console.log('Base URL:', config.baseURL)
      // console.log('Full URL:', `${config.baseURL}${config.url}`)
      // console.log('Headers:', config.headers)
      console.log('Data:', config.data)
      console.groupEnd()
    }

    return config
  },
  (error) => {
    console.error('Request interceptor error:', error)
    return Promise.reject(error)
  }
)

/**
 * Response interceptor for error handling and token management
 */
apiClient.interceptors.response.use(
  (response) => {
    // Enhanced logging for debugging network issues
    if (isApiDebugEnabled()) {
      console.group(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url}`)
      console.log('Status:', JSON.stringify(response.status))
      // console.log('Headers:', JSON.stringify(response.headers))
      // console.log('Data:', JSON.stringify(response.data))
      console.groupEnd()
    }

    // Check for application-level authentication errors
    if (response.data && response.data.data && response.data.data.code === 401) {
      console.warn('Application-level 401 detected:', response.data.data.message)
      handleUnauthorized(response.data.data.message)
      // Return a rejected promise to prevent further processing
      return Promise.reject(new Error(response.data.data.message || 'Session expired'))
    }

    // Check for session expiration messages
    if (response.data && response.data.data &&
        (response.data.data.type === 'authentication' ||
         (response.data.data.message && response.data.data.message.toLowerCase().includes('session')))) {
      console.warn('Session expiration detected:', response.data.data.message)
      handleUnauthorized(response.data.data.message)
      return Promise.reject(new Error(response.data.data.message || 'Session expired'))
    }

    return response
  },
  (error) => {
    // Enhanced error logging for debugging network issues
    if (isApiDebugEnabled()) {
      console.group(`API Error: ${error.config?.method?.toUpperCase()} ${error.config?.url}`)
      console.error('Status:', error.response?.status)
      console.error('Error Message:', error.message)
      console.error('Response Data:', error.response?.data)
      console.error('Request Config:', error.config)
      console.groupEnd()
    }
    
    // Handle different error scenarios
    if (error.response) {
      const status = error.response.status
      
      switch (status) {
        case 401:
          // Unauthorized - clear auth data and redirect to login
          handleUnauthorized('Your session has expired or is invalid. Please log in again.')
          break
          
        case 403:
          // Forbidden - user doesn't have permission
          console.warn('Access forbidden - insufficient permissions')
          break
          
        case 422:
          // Validation error
          console.warn('Validation error:', error.response.data)
          break
          
        case 429:
          // Rate limiting
          console.warn('Rate limit exceeded')
          break
          
        case 500:
          // Server error
          console.error('Server error:', error.response.data)
          break
          
        case 503:
          // Service unavailable
          console.error('Service unavailable')
          break
          
        default:
          console.error('API error:', status, error.response.data)
      }
    } else if (error.request) {
      // Network error
      console.error('Network error - no response received:', error.request)
    } else {
      // Request setup error
      console.error('Request setup error:', error.message)
    }
    
    return Promise.reject(error)
  }
)

/**
 * Handle unauthorized access
 */
const handleUnauthorized = async (reason?: string) => {
  console.warn('Unauthorized access detected. Logging out...', reason)

  try {
    // Try to use auth store for proper logout
    const { useAuthStore } = await import('@/stores/auth')
    const authStore = useAuthStore()
    // await authStore.logout(reason || 'Your session has expired or is invalid. Please log in again.')
  } catch (authError) {
    console.error('Failed to logout via auth store:', authError)

    // Fallback: Clear all auth-related data manually
    const authKeys = [
      'token',
      'user',
      'permissions',
      'role',
      'selectedPartnerId'
    ]

    authKeys.forEach(key => localStorage.removeItem(key))

    // Redirect to login page
    if (typeof window !== 'undefined') {
      // Check if we're not already on the login page to avoid infinite redirects
      if (!window.location.pathname.includes('/login')) {
        window.location.href = '/login'
      }
    }
  }
}

/**
 * Create a new API client instance with custom configuration
 */
export const createApiClient = (config: Partial<AxiosRequestConfig> = {}): AxiosInstance => {
  return axios.create({
    baseURL: API_BASE_URL,
    timeout: getApiTimeout(),
    headers: {
      'Content-Type': 'application/json',
    },
    ...config
  })
}

/**
 * API client for file uploads
 */
export const fileApiClient: AxiosInstance = createApiClient({
  headers: {
    'Content-Type': 'multipart/form-data',
  },
  timeout: 60000 // Longer timeout for file uploads
})

// Add the same interceptors to file upload client
fileApiClient.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // Get authentication data from localStorage
    const token = localStorage.getItem('token')
    const selectedClientId = localStorage.getItem('selectedClientId')
    const selectedPartnerId = localStorage.getItem('selectedPartnerId')
    const clientMode = localStorage.getItem('clientMode')

    // Add standard headers
    config.headers = config.headers || {}
    config.headers['X-App-Key'] = getAppKey()
    config.headers['X-Authorization'] = getAuthKey()

    // Add token if available
    if (token) {
      config.headers['X-Access'] = token
    }

    // Add client context if available
    if (selectedClientId) {
      config.headers['X-Client-ID'] = selectedClientId
    }

    // Note: X-Partner-ID header removed as backend doesn't expect it
    // Partner filtering will be handled via partner_ids parameter in request payload

    if (clientMode) {
      config.headers['X-Client-Mode'] = clientMode
    }

    // For POST requests with FormData, also include access_token
    if (config.method === 'post' && token && config.data instanceof FormData) {
      config.data.append('access_token', token)
    }

    return config
  },
  (error) => {
    console.error('Request interceptor error:', error)
    return Promise.reject(error)
  }
)

fileApiClient.interceptors.response.use(
  (response) => {
    // Check for application-level authentication errors
    if (response.data && response.data.data && response.data.data.code === 401) {
      console.warn('Application-level 401 detected:', response.data.data.message)
      handleUnauthorized(response.data.data.message)
      return Promise.reject(new Error(response.data.data.message || 'Session expired'))
    }

    // Check for session expiration messages
    if (response.data && response.data.data &&
        (response.data.data.type === 'authentication' ||
         (response.data.data.message && response.data.data.message.toLowerCase().includes('session')))) {
      console.warn('Session expiration detected:', response.data.data.message)
      handleUnauthorized(response.data.data.message)
      return Promise.reject(new Error(response.data.data.message || 'Session expired'))
    }

    return response
  },
  (error) => {
    // Handle different error scenarios
    if (error.response) {
      const status = error.response.status

      switch (status) {
        case 401:
          // Unauthorized - clear auth data and redirect to login
          handleUnauthorized('Your session has expired or is invalid. Please log in again.')
          break

        case 403:
          // Forbidden - user doesn't have permission
          console.warn('Access forbidden - insufficient permissions')
          break

        case 422:
          // Validation error
          console.warn('Validation error:', error.response.data)
          break

        case 429:
          // Rate limiting
          console.warn('Rate limit exceeded')
          break

        case 500:
          // Server error
          console.error('Server error:', error.response.data)
          break

        case 503:
          // Service unavailable
          console.error('Service unavailable')
          break

        default:
          console.error('API error:', status, error.response.data)
      }
    } else if (error.request) {
      // Network error
      console.error('Network error - no response received:', error.request)
    } else {
      // Request setup error
      console.error('Request setup error:', error.message)
    }

    return Promise.reject(error)
  }
)

/**
 * Health check endpoint
 */
export const healthCheck = async (): Promise<boolean> => {
  try {
    const response = await apiClient.get('/health')
    return response.status === 200
  } catch (error) {
    console.error('Health check failed:', error)
    return false
  }
}

/**
 * Get API base URL
 */
export const getApiBaseUrl = (): string => {
  return API_BASE_URL
}

/**
 * Check if API is available
 */
export const isApiAvailable = async (): Promise<boolean> => {
  try {
    // In development, use the proxy endpoint for health check
    const checkUrl = import.meta.env.DEV ? '/api/health' : `${API_BASE_URL}health`
    await fetch(checkUrl, {
      method: 'HEAD',
      mode: import.meta.env.DEV ? 'cors' : 'no-cors'
    })
    return true
  } catch (error) {
    console.error('API availability check failed:', error)
    return false
  }
}

export default apiClient
