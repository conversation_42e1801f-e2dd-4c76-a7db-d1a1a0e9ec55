import { apiClient } from './apiClient'
import { createHash<PERSON><PERSON> } from '@/utils/hash'
import type { ApiResponse, PaginationParams, PaginatedResponse } from './types'

/**
 * Get current timestamp for API requests
 */
const getCurrentTimestamp = (): number => {
  return Math.floor(Date.now() / 1000)
}

/**
 * Get authentication headers for API requests
 */
const getAuthHeaders = () => {
  const token = localStorage.getItem('token')
  const selectedClientId = localStorage.getItem('selectedClientId')
  const selectedPartnerId = localStorage.getItem('selectedPartnerId')

  const headers: Record<string, string> = {}

  if (token) {
    headers['X-Access'] = token
  }

  if (selectedClientId) {
    headers['X-Client-ID'] = selectedClientId
  }

  if (selectedPartnerId) {
    headers['X-Partner-ID'] = selectedPartnerId
  }

  return headers
}

export interface Bet {
  id: number
  partner_id: number
  user_id: number
  bet_slip_id: string
  bet_amount: number
  potential_win: number
  actual_win?: number
  status: string
  bet_type: string
  selections: BetSelection[]
  created_at: string
  updated_at: string
}

export interface BetSelection {
  id: number
  bet_id: number
  event_id: string
  market_id: string
  selection_id: string
  odds: number
  status: string
}

export interface BetSlip {
  id: string
  partner_id: number
  user_id: number
  total_amount: number
  potential_win: number
  actual_win?: number
  status: string
  bet_count: number
  created_at: string
  bets: Bet[]
}

export const betsApi = {
  /**
   * Get partner bets
   */
  async getPartnerBets(params: PaginationParams & {
    partner_id?: string
    user_id?: string
    status?: string
    bet_type?: string
  } = {}): Promise<ApiResponse<PaginatedResponse<Bet>>> {
    try {
      const payload = {
        page: params.page || 1,
        limit: params.limit || 10,
        partner_id: params.partner_id || '',
        user_id: params.user_id || '',
        status: params.status || '',
        bet_type: params.bet_type || '',
        start: params.start || '',
        end: params.end || '',
        export: params.export || false,
        timestamp: getCurrentTimestamp()
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post('bets/v1/partner_bets', payload, {
        headers: {
          'X-Hash-Key': hash,
          ...getAuthHeaders()
        }
      })

      const responseData = response.data.data

      // Map the API response to match expected interface
      const bets = responseData.data?.data || []
      const mappedBets = bets.map((bet: any) => ({
        id: parseInt(bet.bet_id || bet.id),
        partner_id: parseInt(bet.partner_id),
        user_id: parseInt(bet.user_id),
        bet_slip_id: bet.bet_slip_id || bet.slip_id,
        bet_amount: parseFloat(bet.bet_amount || bet.amount),
        potential_win: parseFloat(bet.potential_win || bet.potential_payout),
        actual_win: bet.actual_win ? parseFloat(bet.actual_win) : undefined,
        status: bet.status,
        bet_type: bet.bet_type || bet.type,
        selections: bet.selections || [],
        created_at: bet.created_at || new Date().toISOString(),
        updated_at: bet.updated_at || new Date().toISOString()
      }))

      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: {
          data: mappedBets,
          total: parseInt(responseData.data?.total) || 0,
          current_page: params.page || 1,
          limit: payload.limit
        },
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Get partner bets error:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: {
            data: [],
            total: 0,
            current_page: 1,
            limit: params.limit || 10
          },
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: {
          data: [],
          total: 0,
          current_page: 1,
          limit: params.limit || 10
        },
        code: '500'
      }
    }
  },

  /**
   * Get partner bet slips
   */
  async getPartnerBetSlips(partnerId: string, params: PaginationParams & {
    user_id?: string
    status?: string
  } = {}): Promise<ApiResponse<PaginatedResponse<BetSlip>>> {
    try {
      const payload = {
        page: params.page || 1,
        limit: params.limit || 10,
        user_id: params.user_id || '',
        status: params.status || '',
        start: params.start || '',
        end: params.end || '',
        export: params.export || false,
        timestamp: getCurrentTimestamp()
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post(`bets/v1/partners_bet_slips/${partnerId}`, payload, {
        headers: {
          'X-Hash-Key': hash,
          ...getAuthHeaders()
        }
      })

      const responseData = response.data.data

      // Map the API response to match expected interface
      const betSlips = responseData.data?.data || []
      const mappedBetSlips = betSlips.map((slip: any) => ({
        id: slip.bet_slip_id || slip.id,
        partner_id: parseInt(slip.partner_id),
        user_id: parseInt(slip.user_id),
        total_amount: parseFloat(slip.total_amount || slip.amount),
        potential_win: parseFloat(slip.potential_win || slip.potential_payout),
        actual_win: slip.actual_win ? parseFloat(slip.actual_win) : undefined,
        status: slip.status,
        bet_count: parseInt(slip.bet_count || slip.bets_count || 0),
        created_at: slip.created_at || new Date().toISOString(),
        bets: slip.bets || []
      }))

      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: {
          data: mappedBetSlips,
          total: parseInt(responseData.data?.total) || 0,
          current_page: params.page || 1,
          limit: payload.limit
        },
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Get partner bet slips error:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: {
            data: [],
            total: 0,
            current_page: 1,
            limit: params.limit || 10
          },
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: {
          data: [],
          total: 0,
          current_page: 1,
          limit: params.limit || 10
        },
        code: '500'
      }
    }
  }
}
