<template>
  <div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-[#20ad79]/80 to-[#182232]/80 py-12 px-4 sm:px-6 lg:px-8 bg-gray-900">
    <div class="max-w-md w-full space-y-8">
      <div>
        <div class="mx-auto h-20 w-auto flex items-center justify-center">
          <img src="@/assets/moss_logo_full.png" alt="Mossbets B2B Logo" class="h-20 w-auto" />
        </div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-300">
          Verify Your Identity
        </h2>
        <p class="mt-2 text-center text-sm text-gray-300">
          {{ greeting }}, {{ displayName }}!
        </p>
        <p class="mt-1 text-center text-sm text-gray-300">
          Please enter the verification code sent to your mobile number
        </p>
        <div v-if="otpExpiresIn" class="mt-2 text-center text-xs text-orange-600">
          Code expires in {{ otpExpiresIn }} minutes
        </div>
      </div>

      <form class="mt-8 space-y-6" @submit.prevent="handleVerifyOTP">
        <div class="rounded-md shadow-sm -space-y-px">
          <div>
            <label for="verification_code" class="sr-only">Verification Code</label>
            <input
              id="verification_code"
              v-model="verificationCode"
              type="text"
              required
              maxlength="6"
              class="appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm text-center text-2xl tracking-widest"
              placeholder="xxxxx"
              @input="formatCode"
            />
          </div>
        </div>

        <!-- Error Message -->
        <div v-if="error" class="rounded-md bg-red-50 p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">{{ error }}</h3>
            </div>
          </div>
        </div>

        <!-- Success Message -->
        <div v-if="successMessage" class="rounded-md bg-green-50 p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-green-800">{{ successMessage }}</h3>
            </div>
          </div>
        </div>

        <!-- OTP Countdown Timer -->
        <div v-if="otpCountdownDisplay" class="rounded-md bg-blue-50 p-4">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-blue-800">
                Code expires in: <span class="font-medium">{{ otpCountdownDisplay }}</span>
              </p>
            </div>
          </div>
        </div>

        <div>
          <button
            type="submit"
            :disabled="loading || verificationCode.length < 4"
            class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
          >
            <svg v-if="loading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
            </svg>
            {{ loading ? 'Verifying...' : 'Verify Code' }}
          </button>
        </div>

        <div class="flex items-center justify-between">
          <button
            type="button"
            @click="goBackToLogin"
            class="text-sm text-blue-600 hover:text-blue-500 focus:outline-none focus:underline"
          >
            ← Back to Login
          </button>
          
          <button
            type="button"
            @click="resendCode"
            :disabled="loading || resendCooldown > 0"
            class="text-sm text-blue-600 hover:text-blue-500 focus:outline-none focus:underline disabled:text-gray-400 disabled:cursor-not-allowed"
          >
            {{ resendCooldown > 0 ? `Resend in ${resendCooldown}s` : 'Resend Code' }}
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- Partner Selection Modal -->
  <PartnerSelectionModal
    :is-open="showPartnerSelection"
    :partner-ids="userPartnerIds"
    :allow-close="false"
    :show-all-option="true"
    @selected="handlePartnerSelection"
  />
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { authApi } from '@/services/authApi'
import PartnerSelectionModal from '@/components/Modals/PartnerSelectionModal.vue'

// Router and store
const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// Reactive data
const loading = ref(false)
const error = ref('')
const successMessage = ref('')
const verificationCode = ref('')
const resendCooldown = ref(0)
const otpCountdown = ref(0)
const showPartnerSelection = ref(false)
const userPartnerIds = ref<number[]>([])
let resendTimer: number | null = null
let otpTimer: number | null = null

// Get user data from route params or auth store context
const userData = computed(() => {
  if (route.params.userData) {
    return JSON.parse(route.params.userData as string)
  }
  const context = authStore.getOtpContext()
  return context?.userData || null
})

const loginCredentials = computed(() => {
  if (route.params.credentials) {
    return JSON.parse(route.params.credentials as string)
  }
  const context = authStore.getOtpContext()
  return context?.credentials || null
})

// Computed properties for display
const displayName = computed(() => {
  if (userData.value?.username) {
    let username = userData.value.username

    // Try to decode base64 if it looks encoded
    try {
      if (username.match(/^[A-Za-z0-9+/]+=*$/)) {
        const decoded = atob(username)
        if (decoded && decoded.length > 0 && !decoded.includes('\x00')) {
          username = decoded
        }
      }
    } catch (e) {
      // Not base64, use as is
    }

    // Extract name from email
    if (username.includes('@')) {
      const namePart = username.split('@')[0]
      // Convert dots and underscores to spaces and capitalize
      return namePart
        .replace(/[._]/g, ' ')
        .split(' ')
        .map((word: string) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
        .join(' ')
    }

    // Capitalize first letter if it's a simple name
    return username.charAt(0).toUpperCase() + username.slice(1).toLowerCase()
  }
  return 'User'
})

const greeting = computed(() => {
  const hour = new Date().getHours()
  const day = new Date().getDay()
  const isWeekend = day === 0 || day === 6

  let timeGreeting = ''
  if (hour >= 5 && hour < 12) {
    timeGreeting = 'Good morning'
  } else if (hour >= 12 && hour < 17) {
    timeGreeting = 'Good afternoon'
  } else if (hour >= 17 && hour < 21) {
    timeGreeting = 'Good evening'
  } else {
    timeGreeting = 'Good evening'
  }

  // Add weekend context
  if (isWeekend && (hour >= 9 && hour <= 11)) {
    return `${timeGreeting} and happy ${day === 0 ? 'Sunday' : 'Saturday'}`
  }

  return timeGreeting
})

const otpExpiresIn = computed(() => {
  return userData.value?.otp_expires_in || null
})

const otpCountdownDisplay = computed(() => {
  if (otpCountdown.value <= 0) return null
  const minutes = Math.floor(otpCountdown.value / 60)
  const seconds = otpCountdown.value % 60
  return `${minutes}:${seconds.toString().padStart(2, '0')}`
})

// Methods
const formatCode = (event: Event) => {
  const target = event.target as HTMLInputElement
  // Only allow numbers
  // target.value = target.value.replace(/[^0-9]/g, '')
  // Alphanumeric
  target.value = target.value.replace(/[^a-zA-Z0-9]/g, '')
  verificationCode.value = target.value
}

const handleVerifyOTP = async () => {
  if (!loginCredentials.value) {
    error.value = 'Login session expired. Please start over.'
    return
  }

  error.value = ''
  successMessage.value = ''
  loading.value = true

  try {
    const credentials = {
      ...loginCredentials.value,
      verification_code: verificationCode.value
    }

    const result = await authStore.loginWithCode(credentials)
    
    if (result.success) {
      // Clear OTP context on successful verification
      authStore.clearOtpContext()
      successMessage.value = 'Verification successful!'

      // Check if partner selection is required
      if (result.requiresPartnerSelection && result.partners && result.partners.length > 1) {
        // Multiple partners - show selection modal
        userPartnerIds.value = result.partners.map((p: any) => p.id)
        showPartnerSelection.value = true
      } else {
        // Single partner, no partners, or already handled by auth store - proceed to dashboard
        setTimeout(() => {
          router.push({ name: 'dashboard' })
        }, 1000)
      }
    } else {
      error.value = result.message || 'Verification failed'
    }
  } catch (err: any) {
    error.value = err.message || 'Verification failed'
  } finally {
    loading.value = false
  }
}

const goBackToLogin = () => {
  router.push({ name: 'login' })
}

const resendCode = async () => {
  if (!loginCredentials.value) {
    error.value = 'Login session expired. Please start over.'
    return
  }

  loading.value = true
  error.value = ''

  try {
    // Call the resendOTP function which handles already encoded passwords
    const result = await authStore.resendOTP(loginCredentials.value)

    if (result.success && result.requiresCode) {
      successMessage.value = 'Verification code resent successfully!'
      startResendCooldown()
      // Restart OTP countdown with new expiration time
      stopOtpCountdown()
      startOtpCountdown()
    } else {
      error.value = result.message || 'Failed to resend code. Please try again.'
    }
  } catch (err: any) {
    error.value = err.message || 'Failed to resend code'
  } finally {
    loading.value = false
  }
}

const startResendCooldown = () => {
  resendCooldown.value = 60 // 60 seconds cooldown
  resendTimer = setInterval(() => {
    resendCooldown.value--
    if (resendCooldown.value <= 0) {
      clearInterval(resendTimer!)
      resendTimer = null
    }
  }, 1000)
}

const startOtpCountdown = () => {
  const expiresIn = otpExpiresIn.value
  if (expiresIn && expiresIn > 0) {
    otpCountdown.value = expiresIn * 60 // Convert minutes to seconds
    otpTimer = setInterval(() => {
      otpCountdown.value--
      if (otpCountdown.value <= 0) {
        clearInterval(otpTimer!)
        otpTimer = null
        error.value = 'OTP has expired. Please request a new code.'
      }
    }, 1000)
  }
}

const stopOtpCountdown = () => {
  if (otpTimer) {
    clearInterval(otpTimer)
    otpTimer = null
  }
  otpCountdown.value = 0
}

const clearError = () => {
  error.value = ''
  successMessage.value = ''
}

const handlePartnerSelection = async (partnerId: number | null) => {
  try {
    // Set selected partner in auth store
    authStore.selectedPartnerId = partnerId?.toString() || null
    showPartnerSelection.value = false

    // Navigate to dashboard
    setTimeout(() => {
      router.push({ name: 'dashboard' })
    }, 500)
  } catch (error) {
    console.error('Failed to set partner:', error)
  }
}

// Lifecycle
onMounted(() => {
  // Check if we have the required data
  if (!userData.value || !loginCredentials.value) {
    router.push({ name: 'login' })
    return
  }

  // Auto-focus the input
  const input = document.getElementById('verification_code')
  if (input) {
    input.focus()
  }

  // Start OTP countdown timer
  startOtpCountdown()
})

onUnmounted(() => {
  if (resendTimer) {
    clearInterval(resendTimer)
  }
  if (otpTimer) {
    clearInterval(otpTimer)
  }
})

// Watch for input changes to clear errors
import { watch } from 'vue'
watch(() => verificationCode.value, clearError)
</script>
