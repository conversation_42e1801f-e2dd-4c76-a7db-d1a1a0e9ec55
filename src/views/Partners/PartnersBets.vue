<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
  

    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Total Bets</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ totalBets.toLocaleString() }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Total Volume</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ formatCurrency(totalVolume) }}</p>
            <p class="text-xs text-gray-500 mt-1 dark:text-gray-300">Avg: {{ formatCurrency(averageBetAmount) }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-yellow-100 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Active Partners</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ activePartners }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-100 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Potential Win</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ formatCurrency(totalPossibleWin) }}</p>
            <p class="text-xs text-gray-500 mt-1 dark:text-gray-300">Avg Odds: {{ averageOdds.toFixed(2) }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label for="partner-filter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Partner</label>
          <select id="partner-filter" v-model="filters.partner_id" @change="applyFilters"
            class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm">
            <option value="">All Partners</option>
            <option v-for="partner in partners" :key="partner.id" :value="partner.id">
              {{ partner.name }}
            </option>
          </select>
        </div>
        <div>
          <label for="status-filter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Bet Status</label>
          <select id="status-filter" v-model="filters.bet_status" @change="applyFilters"
            class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm">
            <option value="">All Statuses</option>
            <option value="pending">Pending</option>
            <option value="won">Won</option>
            <option value="lost">Lost</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>
        <div>
          <label for="sport-filter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Sport</label>
          <select id="sport-filter" v-model="filters.sport_type" @change="applyFilters"
            class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm">
            <option value="">All Sports</option>
            <option value="football">Football</option>
            <option value="basketball">Basketball</option>
            <option value="tennis">Tennis</option>
            <option value="cricket">Cricket</option>
          </select>
        </div>
        <div>
          <label for="date-filter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Date Range</label>
          <select id="date-filter" v-model="filters.date_range" @change="applyFilters"
            class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm">
            <option value="">All Time</option>
            <option value="today">Today</option>
            <option value="week">This Week</option>
            <option value="month">This Month</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Partners Bets Table -->
    <!-- Bets Tab -->
    <DataTable :data="partnersBets" :headers="betsHeaders" title="Partner Bets" :loading="loading" :searchable="false"
      :pagination="true" :current-page="1" :total-records="partnersBetsCount || 0" :page-size="10" :has-actions="true"
      empty-message="No bets found" @row-click="handleRowClick">
      <!-- Header Actions -->
      <template #header-actions>

        <button @click="refreshData" :disabled="loading"
          class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200">
          <svg v-if="loading" class="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor"
            viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          <svg v-else class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          {{ loading ? 'Refreshing...' : 'Refresh' }}
        </button>
        <button @click="exportData"
          class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
          <svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          Export
        </button>
      </template>

      <!-- Custom Bet Reference Cell -->
      <template #cell-bet_reference="{ value, item }">
        <div class="text-sm">
          <div class="font-medium dark:text-gray-300 text-gray-900">{{ value || 'n/a' }}</div>
          <div class="text-gray-500 dark:text-gray-300">ID: {{ item.bet_id }}</div>
        </div>
      </template>

      <!-- Custom Bet Amount Cell -->
      <template #cell-bet_amount="{ value }">
        <span class="font-medium dark:text-orange-300 text-gray-900">{{ formatCurrency(value) }}</span>
      </template>

      <!-- Custom Potential Win Cell -->
      <template #cell-possible_win="{ value }">
        <span class="font-medium dark:text-green-300 text-gray-900">{{ formatCurrency(value) }}</span>
      </template>

      <!-- BetType -->
      <template #cell-bet_type="{ value }">
        <span :class="{
          'bg-green-100 text-green-800': value === '1',
          'bg-red-100 text-red-800': value === '0',
          'bg-orange-100 text-orange-800': value === '3'
        }" class="inline-flex items-center px-2.5 py-1.5 rounded-full text-xs font-medium">
          {{ value === '1' ? 'Cash' : value === '0' ? 'Bonus' : value === '3' ? 'Free' : '-' }}
        </span>
      </template>

      <!-- Custom Status Cell -->
      <template #cell-status="{ value }">
        <span :class="{
          'bg-yellow-100 text-yellow-800': value === '0',
          'bg-green-100 text-green-800': value === '1',
          'bg-red-100 text-red-800': value === '2',
          'bg-gray-100 text-gray-800': value === '3'
        }" class="inline-flex items-center px-2.5 py-1.5 rounded-full text-xs font-medium capitalize">
          {{ value === '0' ? 'Pending' : value === '1' ? 'Won' : value === '2' ? 'Lost' : value === '3' ? 'Cancelled' :
          '-' }}
        </span>
      </template>

      <!-- Actions -->
      <template #actions="{ item, closeDropdown }">
        <button @click="viewBetDetails(item); closeDropdown()"
          class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-blue-100 transition-colors duration-200">
          <svg class="inline w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
          </svg>
          View Bet Details
        </button>
        <button @click="viewBetSlip(item); closeDropdown()"
          class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-blue-100 transition-colors duration-200">
          <svg class="inline w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
          </svg>
          View Bet Slip
        </button>
      </template>
    </DataTable>

    <!-- Bet Deatils Modal -->

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { formatDate, formatCurrency } from '@/utils/formatters'
import DataTable from '@/components/DataTable.vue'
import { partnerApi } from '@/services/partnerApi'
import { navigateWithCache } from '@/utils/navigationCache'

// Router
const router = useRouter()
const route = useRoute()

// Reactive data
const loading = ref(false)
const partnersBets = ref<any[]>([])
const partnersBetsCount = ref(0)
const partners = ref<any[]>([])
const currentPage = ref(1)
const totalRecords = ref(0)
const pageSize = ref(10)
const searchQuery = ref('')
const sortField = ref('')
const sortDirection = ref<'asc' | 'desc'>('asc')

// Filters
const filters = reactive({
  partner_id: '',
  bet_status: '',
  sport_type: '',
  date_range: ''
})

const betsHeaders = computed(() => ({
  bet_reference: 'Bet Reference',
  // bet_id: 'Bet ID',
  profile_id: 'Profile',
  partner_name: 'Partner',
  bet_amount: 'Bet Amount',
  possible_win: 'Possible Win',
  total_odd: 'Total Odds',
  total_games: 'Total Games',
  live_events: 'Live Events',
  bet_type: 'Bet Type',
  status: 'Status',
  created_at: 'Created'
}))


// Computed properties for summary cards
const totalBets = computed(() => partnersBets.value.length)

const totalVolume = computed(() => {
  return partnersBets.value.reduce((sum, bet) => {
    // Convert string to number and handle different currency formats
    const amount = parseFloat(bet.bet_amount || '0')
    return sum + amount
  }, 0)
})

const activePartners = computed(() => {
  // Get unique partner IDs from the bets data
  const uniquePartnerIds = new Set(
    partnersBets.value
      .map(bet => bet.partner_id)
      .filter(id => id) // Remove null/undefined values
  )
  return uniquePartnerIds.size
})

const winRate = computed(() => {
  if (totalBets.value === 0) return 0

  // Count bets by status - based on your data, status "0" = Pending, need to check for won bets
  // You'll need to update this based on your actual status values for won bets
  const wonBets = partnersBets.value.filter(bet => {
    // Update this condition based on your actual status values
    // From your data: status "0" = Pending, you'll need to identify won status
    return bet.status === '1' || bet.status_description === 'Won'
  }).length

  return Math.round((wonBets / totalBets.value) * 100)
})

// Additional computed properties for more detailed analytics
const totalPossibleWin = computed(() => {
  return partnersBets.value.reduce((sum, bet) => {
    const possibleWin = parseFloat(bet.possible_win || '0')
    return sum + possibleWin
  }, 0)
})

const averageBetAmount = computed(() => {
  if (totalBets.value === 0) return 0
  return totalVolume.value / totalBets.value
})

const averageOdds = computed(() => {
  if (totalBets.value === 0) return 0
  const totalOdds = partnersBets.value.reduce((sum, bet) => {
    const odds = parseFloat(bet.total_odd || '0')
    return sum + odds
  }, 0)
  return totalOdds / totalBets.value
})

const betsByStatus = computed(() => {
  const statusCounts = {
    pending: 0,
    won: 0,
    lost: 0,
    cancelled: 0
  }

  partnersBets.value.forEach(bet => {
    // Map status codes to status names based on your data structure
    let statusKey = 'pending' // default

    if (bet.status_description) {
      const statusDesc = bet.status_description.toLowerCase()
      if (statusDesc.includes('pending')) statusKey = 'pending'
      else if (statusDesc.includes('won') || statusDesc.includes('win')) statusKey = 'won'
      else if (statusDesc.includes('lost') || statusDesc.includes('lose')) statusKey = 'lost'
      else if (statusDesc.includes('cancelled') || statusDesc.includes('cancel')) statusKey = 'cancelled'
    } else if (bet.status !== undefined) {
      // Map numeric status codes (adjust these based on your system)
      const status = bet.status.toString()
      switch (status) {
        case '0': statusKey = 'pending'; break
        case '1': statusKey = 'won'; break
        case '2': statusKey = 'lost'; break
        case '3': statusKey = 'cancelled'; break
        default: statusKey = 'pending'
      }
    }

    statusCounts[statusKey as keyof typeof statusCounts]++
  })

  return statusCounts
})

const betsByCurrency = computed(() => {
  const currencyTotals: Record<string, { count: number; volume: number }> = {}

  partnersBets.value.forEach(bet => {
    const currency = bet.bet_currency || 'Unknown'
    const amount = parseFloat(bet.bet_amount || '0')

    if (!currencyTotals[currency]) {
      currencyTotals[currency] = { count: 0, volume: 0 }
    }

    currencyTotals[currency].count++
    currencyTotals[currency].volume += amount
  })

  return currencyTotals
})

// Methods

const loadData = async () => {
  loading.value = true
  try {
    const payload = {
      page: currentPage.value,
      limit: pageSize.value,
      partner_ids: filters.partner_id || route.query.partner_id as string || '',
      status: filters.bet_status,
      search: searchQuery.value
    }

    console.log('Loading partner bets with payload:', payload)

    const response = await partnerApi.getPartnerBets(payload)
    console.log('Partner bets API response:', response)

    if (response.status === 200) {
      partnersBets.value = response.message.data || []
      totalRecords.value = response.message.total || 0
      console.log('Loaded', partnersBets.value.length, 'bets')
    } else {
      console.error('Failed to load partner bets:', response)
      partnersBets.value = []
      totalRecords.value = 0
    }

    // Load partners for filter dropdown
    const partnersResponse = await partnerApi.getPartners({ limit: 100 })
    if (partnersResponse.status === 200) {
      partners.value = partnersResponse.message.data || []
    }

  } catch (error) {
    console.error('Error loading partners bets:', error)
    partnersBets.value = []
    totalRecords.value = 0
  } finally {
    loading.value = false
  }
}


const viewBetSlip = (bet: any) => {
  console.log('Navigating to bet slip for bet:', bet)

  // Store bet data for caching
  // storeNavigationData(bet, 'bet')

  // Navigate to bet slips page with bet_id as route parameter
  // router.push({
  //   name: 'partners-bet-slips',
  //   params: { id: bet.bet_id.toString() },
  //   query: {
  //     partner_id: bet.partner_id.toString(),
  //     bet_reference: bet.bet_reference
  //   }
  // })


  navigateWithCache(
    router,
    bet,
    'bet',
    'partners-bet-slips',
    { id: bet.bet_id.toString() }
  )
}
const refreshData = () => {
  loadData()
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  loadData()
}

const handleSearch = (query: string) => {
  searchQuery.value = query
  currentPage.value = 1
  loadData()
}

const handleSort = (field: string, direction: 'asc' | 'desc') => {
  sortField.value = field
  sortDirection.value = direction
  loadData()
}

const handleRowClick = (item: any) => {
  viewBetDetails(item)
}

const applyFilters = () => {
  currentPage.value = 1
  loadData()
}

const viewBetDetails = (bet: any) => {
  console.log('View bet details:', bet)
  // Navigate to bet details page or open modal
  router.push({ name: 'partners-bet-details', query: { bet_id: bet.bet_id, partner_id: bet.partner_id } })
}

const exportData = () => {
  console.log('Export partners bets data')
  // Implement export logic
}

// Lifecycle
onMounted(() => {
  loadData()
})
</script>
