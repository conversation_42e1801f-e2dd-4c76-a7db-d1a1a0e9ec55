<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <!-- <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">System Users</h1>
        <p class="mt-1 text-sm text-gray-500">
          Manage system users and their access permissions
        </p>
      </div>
      <div class="flex items-center space-x-3">
        <router-link v-if="authStore.isSuperUser" :to="{ name: 'add-user' }"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
          <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
          </svg>
          Add System User
        </router-link>
      </div>
    </div> -->

    <!-- Search and Filters -->
    <SearchFilter v-model:filters="searchFilters" search-placeholder="Search users by name, email, or username..."
      :show-status-filter="true" :show-role-filter="true" :show-date-filter="true"
      :show-client-filter="authStore.isSuperUser" :show-partner-filter="true" :has-advanced-filters="true"
      :role-options="roles" @search="handleSearch" @filter="handleFilter" @clear="handleClearFilters" />

    <!-- Users Data Table -->
    <DataTable :data="data" :headers="tableHeaders" title="System Users" :loading="isLoading" :searchable="true"
      :pagination="true" :current-page="offset" :total-records="total" :page-size="limit" :has-actions="true"
      empty-message="No users found" @page-change="gotToPage" @search="handleSearch" @row-click="handleRowClick"
      @limit-change="handleLimitChange">
      <!-- Header Actions -->
      <template #header-actions>
        <button @click="setUsers"
          class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
          <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          Refresh
        </button>
        <router-link :to="{ name: 'add-user' }"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
          <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Add User
        </router-link>
      </template>

      <template #cell-contact_info="{ item }">
        <div class="text-sm">
          <div class="text-gray-900 dark:text-gray-300">{{ item.user_name }}</div>
          <div v-if="item.msisdn" class="text-gray-500 dark:text-gray-300">+{{ item.msisdn }}</div>
        </div>
      </template>

      <template #cell-partner_info="{ item }">
        <div class="text-sm">
          <div class="text-gray-900 dark:text-gray-300">{{ item.partner_name || 'N/A' }}</div>
          <div class="text-gray-500 dark:text-gray-300">{{ item.partner_id || 'No Partner' }}</div>
        </div>
      </template>

      <template #cell-login_attempts="{ item }">
        <div class="text-xs">
          <div class="text-green-600">Successful: {{ item.cumulative_success_login }}</div>
          <div class="text-red-600">Failed: {{ item.cumlative_failed_attempts }}</div>
        </div>
      </template>

      <template #cell-last_logged_on="{ item }">
        <span class="text-gray-900 dark:text-gray-300">{{ formatLastLoggedOn(item.last_logged_on) }}</span>
      </template>

      <template #cell-status="{ item }">
        <span v-if="parseInt(item.status) === 1"
          class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
          Active
        </span>
        <span v-else-if="parseInt(item.status) === 3"
          class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
          Deactivated
        </span>
        <span v-else class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800">
          Inactive
        </span>
      </template>

      <!-- Actions dropdown -->
      <template #actions="{ item, closeDropdown }">
        <button v-if="parseInt(item.status) === 1" @click="editRow(item); closeDropdown()"
          class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200">
          <svg class="inline w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
          </svg>
          Edit User {{ explodeByUnderscore(item.display_name.split(' ')[0]) }}
        </button>
        <button @click="resend(parseInt(item.user_id)); closeDropdown()"
          class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200">
          <svg class="inline w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
          Send OTP to {{ explodeByUnderscore(item.display_name.split(' ')[0]) }}
        </button>
      </template>
    </DataTable>
  </div>
</template>

<script>
import router from '@/router'
import { useAuthStore } from '@/stores/auth'
import { systemApi } from '@/services/systemApi'
import DataTable from '@/components/DataTable.vue'
import SearchFilter from '@/components/SearchFilter.vue'
import { navigateWithCache } from '@/utils/navigationCache'

export default {
  components: {
    DataTable,
    SearchFilter
  },
  data() {
    return {
      // search
      searchClient: "",
      searchDropdown: false,
      searchDropdownPlaceholder: "",
      clientName: "",
      organisations: [],
      data: [],
      roles: [],

      // Search filters
      searchFilters: {
        search: '',
        status: '',
        role_id: '',
        start_date: '',
        end_date: '',
        client_id: ''
      },

      //
      fullPage: true,
      total: 0,
      offset: 1,
      limit: 100,
      showDropdown: [],

      isOpen: false,
      isLoading: false,

      // Table headers for DataTable
      tableHeaders: {
        display_name: 'Display Name',
        contact_info: 'Email/Phone',
        role_name: 'Role',
        last_logged_on: 'Last Login',
        status: 'Status',
        created_at: 'Created',
      },
      //

      moreParams: {
        start: "",
        end: "",
        limit: 100,
        page: 1,
        status: "",
        timestamp: Date.now(),
        partner_id: "",
      },
      time3: "",
      id: null,
      status: null,
    }
  },

  setup() {
    const authStore = useAuthStore()
    return {
      authStore
    }
  },

  watch: {
    searchClient(newVal, oldVal) {
      if (newVal !== oldVal && newVal !== "") {
        this.filterOrganizations();
      }
    }
  },
  async mounted() {
    await this.setRoles()

    if (!this.authStore.isSuperUser) {
      this.moreParams.partner_id = this.$route?.params?.partner_id || ''
    }

    await this.setUsers()
  },

  methods: {
    //
    formatLastLoggedOn(lastLoggedOn) {
      if (!lastLoggedOn) {
        return 'Never logged in';
      }
      try {
        const date = new Date(lastLoggedOn);
        return date.toLocaleString();
      } catch (error) {
        return 'Never logged in';
      }
    },

    explodeByUnderscore(str) {
      if (!str) return '';

      const names = str.split('_');
      const firstName = names[0] || '';
      const secondName = names[1] || '';
      const thirdName = names[2] || '';

      return `${this.capitalizeFirstLetter(firstName)} ${this.capitalizeFirstLetter(secondName)} ${this.capitalizeFirstLetter(thirdName)}`;
    },

    capitalizeFirstLetter(string) {
      return string.charAt(0).toUpperCase() + string.slice(1);
    },

    // Format role name from role_id
    getRoleName(roleId) {
      const role = this.roles.find(r => r.id === parseInt(roleId))
      return role ? role.name : `Role ${roleId}`
    },

    // Format permissions summary
    formatPermissionsSummary(permissions) {
      if (!permissions) return '0 permissions'

      let count = 0
      if (typeof permissions === 'string') {
        // Handle permissions_acl format (comma or colon separated)
        const separator = permissions.includes(':') ? ':' : ','
        count = permissions.split(separator).filter(p => p.trim()).length
      } else if (Array.isArray(permissions)) {
        count = permissions.length
      }

      return `${count} permission${count !== 1 ? 's' : ''}`
    },

    // Format partner information
    formatPartnerInfo(partners) {
      if (!partners || !Array.isArray(partners) || partners.length === 0) {
        return 'No partners'
      }

      if (partners.length === 1) {
        return partners[0].name || `Partner ${partners[0].id}`
      }

      return `${partners.length} partners`
    },

    // Format contact information
    formatContactInfo(email, phone) {
      const parts = []
      if (email) parts.push(email)
      if (phone) parts.push(`+${phone}`)
      return parts.join(' | ') || 'No contact info'
    },

    // Format timestamp
    formatTimestamp(timestamp) {
      if (!timestamp) return 'Unknown'

      try {
        const date = new Date(timestamp)
        return date.toLocaleDateString('en-GB', {
          day: '2-digit',
          month: '2-digit',
          year: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        }) + ' UTC'
      } catch (error) {
        return 'Invalid date'
      }
    },

    //
    gotToPage(page) {
      let vm = this
      vm.moreParams.offset = page
      vm.offset = page
      vm.setUsers()
    },

    toggleDropdown(index) {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = i === index ? !this.showDropdown[i] : false;
      }
    },

    closeDropdown() {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = false;
      }
    },

    async resend(user_id) {
      let app = this
      const payload = {
        user_id: user_id,
      }

      app.closeDropdown()

      // Simple confirmation for now - can be replaced with SweetAlert later
      if (confirm('Are you sure you want to resend OTP to this user?')) {
        try {
          app.isLoading = true
          const response = await systemApi.resendOTP(payload)
          if (response.status === 200) {
            alert('OTP sent successfully!')
          } else {
            alert('Error sending OTP: ' + response.message)
          }
        } catch (error) {
          console.error('Error sending OTP:', error)
          alert('Error sending OTP')
        } finally {
          app.isLoading = false
        }
      }
    },

    async editRow(user) {
      this.closeDropdown()
      // Store user data for editing
      console.log("DSDA:", JSON.stringify(user))
      navigateWithCache(
        router,
        user,
        'user',
        'edit-user',
        { id: user.user_id }
      )
    },

    async setUsers() {
      this.isLoading = true
      try {
        let response = await systemApi.getUsers(this.moreParams)
       
        this.data = response.message?.data || []

        this.total = parseInt(response.message?.total_count || 0)

        this.showDropdown = []
        for (let i = 0; i < this.data.length; i++) {
          this.showDropdown.push(false)
        }
      } catch (error) {
        console.error('Error fetching users:', error)
        this.data = []
        this.total = 0
      }
      this.isLoading = false
    },

    // Handle search from DataTable
    handleSearch(query) {
      this.searchFilters.search = query
      this.applyFilters()
    },

    // Handle filter changes from SearchFilter component
    handleFilter(filters) {
      Object.assign(this.searchFilters, filters)
      this.applyFilters()
    },

    // Handle clear filters
    handleClearFilters() {
      this.searchFilters = {
        search: '',
        status: '',
        role_id: '',
        start_date: '',
        end_date: '',
        client_id: ''
      }
      this.applyFilters()
    },

    // Apply filters to API call
    applyFilters() {
      // Update moreParams with filter values
      this.moreParams = {
        ...this.moreParams,
        search: this.searchFilters.search,
        status: this.searchFilters.status,
        role_id: this.searchFilters.role_id,
        start: this.searchFilters.start_date,
        end: this.searchFilters.end_date,
        client_id: this.searchFilters.client_id || this.moreParams.client_id,
        page: 1 // Reset to first page when filtering
      }
      this.offset = 1
      this.setUsers()
    },

    // Handle row click from DataTable
    handleRowClick(item, index) {
      // Optional: handle row clicks if needed
      console.log('Row clicked:', item, index)
    },

    // Handle limit change from DataTable
    handleLimitChange(newLimit) {
      this.limit = newLimit
      this.moreParams.limit = newLimit
      this.offset = 1 // Reset to first page
      this.setUsers() // Reload data with new limit
    },

    // Fetch System roles
    async setRoles() {
      let app = this
      try {
        let response = await systemApi.getRoles({ limit: 100 })
        console.log("Roles API response:", JSON.stringify(response))
        if (response.status === 200) {
          app.roles = []
          const rolesData = response.message?.data || response.message || []
          rolesData.forEach(function (item) {
            // Map to the format expected by SearchFilter component
            let role = {
              role_id: item.role_id || item.id,
              role_name: item.role_name || item.name
            }
            app.roles.push(role)
          })
          console.log("Processed roles:", app.roles)
        }
      } catch (error) {
        console.error('Error fetching roles:', error)
        app.roles = []
      }
    },

    // get role name from role id
    getRoleName(role_id) {
      let role = this.roles.find(role => role.value === role_id)
      return role ? role.text : 'N/A'
    }
  }
}
</script>
